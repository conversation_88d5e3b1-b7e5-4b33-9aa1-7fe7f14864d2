import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'file_upload_widget.dart';
import '../profile_marchant/bloc/profile_marchant_bloc.dart';
import '../profile_marchant/bloc/profile_marchant_event.dart';
import '../profile_marchant/bloc/profile_marchant_state.dart';

/// A reusable file upload widget with validation support
/// Can be used with different BLoCs by providing the appropriate context
class ValidatedFileUploadWidget extends StatefulWidget {
  final String label;
  final String uploadId;
  final bool isRequired;
  final String? currentFileId;
  final String? currentFileName;
  final Function(String fileId) onFileUploaded;
  final Function(bool hasFile)? onValidationChanged;
  final String? customValidationError;

  const ValidatedFileUploadWidget({
    super.key,
    required this.label,
    required this.uploadId,
    required this.onFileUploaded,
    this.isRequired = false,
    this.currentFileId,
    this.currentFileName,
    this.onValidationChanged,
    this.customValidationError,
  });

  @override
  State<ValidatedFileUploadWidget> createState() => _ValidatedFileUploadWidgetState();
}

class _ValidatedFileUploadWidgetState extends State<ValidatedFileUploadWidget> {
  String? _validationError;
  bool _hasFile = false;

  @override
  void initState() {
    super.initState();
    _hasFile = widget.currentFileId != null;
    _updateValidationError();
  }

  @override
  void didUpdateWidget(ValidatedFileUploadWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentFileId != widget.currentFileId ||
        oldWidget.customValidationError != widget.customValidationError ||
        oldWidget.isRequired != widget.isRequired) {
      _hasFile = widget.currentFileId != null;
      _updateValidationError();
    }
  }

  void _updateValidationError() {
    setState(() {
      if (widget.customValidationError != null) {
        _validationError = widget.customValidationError;
      } else if (widget.isRequired && !_hasFile) {
        _validationError = '${widget.label} is required';
      } else {
        _validationError = null;
      }
    });
  }

  void _onValidationChanged(bool hasFile) {
    _hasFile = hasFile;
    _updateValidationError();
    if (widget.onValidationChanged != null) {
      widget.onValidationChanged!(hasFile);
    }
  }

  /// Validates the file upload and returns error message if invalid
  String? validate() {
    if (widget.isRequired && !_hasFile) {
      return '${widget.label} is required';
    }
    return widget.customValidationError;
  }

  /// Returns true if the file upload is valid
  bool get isValid => validate() == null;

  /// Returns true if a file has been uploaded
  bool get hasFile => _hasFile;

  @override
  Widget build(BuildContext context) {
    return FileUploadWidget(
      label: widget.label,
      uploadId: widget.uploadId,
      isRequired: widget.isRequired,
      validationError: _validationError,
      currentFileId: widget.currentFileId,
      currentFileName: widget.currentFileName,
      onFileUploaded: widget.onFileUploaded,
      onValidationChanged: _onValidationChanged,
    );
  }
}

/// A specialized version for ProfileMarchant BLoC
class ProfileFileUploadWidget extends StatelessWidget {
  final String label;
  final String uploadId;
  final bool isRequired;
  final String? currentFileId;
  final String? currentFileName;
  final Function(String fileId) onFileUploaded;
  final Function(bool hasFile)? onValidationChanged;

  const ProfileFileUploadWidget({
    super.key,
    required this.label,
    required this.uploadId,
    required this.onFileUploaded,
    this.isRequired = false,
    this.currentFileId,
    this.currentFileName,
    this.onValidationChanged,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
      builder: (context, state) {
        String? validationError;
        
        // Handle specific upload states for this widget
        if (state is ImageUploadError && 
            state.toString().contains(uploadId)) {
          validationError = 'Upload failed. Please try again.';
        }

        return ValidatedFileUploadWidget(
          label: label,
          uploadId: uploadId,
          isRequired: isRequired,
          currentFileId: currentFileId,
          currentFileName: currentFileName,
          customValidationError: validationError,
          onFileUploaded: onFileUploaded,
          onValidationChanged: onValidationChanged,
        );
      },
    );
  }
}
