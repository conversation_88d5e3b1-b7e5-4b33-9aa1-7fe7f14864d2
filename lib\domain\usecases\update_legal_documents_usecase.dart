import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/update_company_details_response.dart';
import '../repositories/profile_marchant_repository.dart';

class UpdateLegalDocumentsUseCase {
  final ProfileMarchantRepository repository;

  UpdateLegalDocumentsUseCase(this.repository);

  Future<Either<Failure, UpdateCompanyDetailsResponse>> call(UpdateLegalDocumentsParams params) async {
    return await repository.updateLegalDocuments(
      companyLegalName: params.companyLegalName,
      companySize: params.companySize,
      crNumber: params.crNumber,
      investedCapital: params.investedCapital,
      investedCapitalUnit: params.investedCapitalUnit,
      crDocument: params.crDocument,
      licenseDocument: params.licenseDocument,
    );
  }
}

class UpdateLegalDocumentsParams {
  final String companyLegalName;
  final String companySize;
  final String crNumber;
  final int investedCapital;
  final String investedCapitalUnit;
  final String crDocument;
  final String licenseDocument;

  UpdateLegalDocumentsParams({
    required this.companyLegalName,
    required this.companySize,
    required this.crNumber,
    required this.investedCapital,
    required this.investedCapitalUnit,
    required this.crDocument,
    required this.licenseDocument,
  });
}
