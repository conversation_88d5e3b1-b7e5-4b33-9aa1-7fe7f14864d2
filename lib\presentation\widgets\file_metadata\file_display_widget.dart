import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/constants/app_colors.dart';
import '../../../data/models/file_metadata_model.dart';
import 'bloc/file_metadata_bloc.dart';
import 'bloc/file_metadata_event.dart';
import 'bloc/file_metadata_state.dart';

class FileDisplayWidget extends StatefulWidget {
  final String? fileId;
  final VoidCallback? onRemove;

  const FileDisplayWidget({
    super.key,
    this.fileId,
    this.onRemove,
  });

  @override
  State<FileDisplayWidget> createState() => _FileDisplayWidgetState();
}

class _FileDisplayWidgetState extends State<FileDisplayWidget> {
  @override
  void initState() {
    super.initState();
    _loadFileMetadata();
  }

  @override
  void didUpdateWidget(FileDisplayWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.fileId != widget.fileId) {
      _loadFileMetadata();
    }
  }

  void _loadFileMetadata() {
    if (widget.fileId != null && widget.fileId!.isNotEmpty) {
      context.read<FileMetadataBloc>().add(LoadFileMetadata(fileId: widget.fileId!));
    } else {
      context.read<FileMetadataBloc>().add(const ClearFileMetadata());
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.fileId == null || widget.fileId!.isEmpty) {
      return const SizedBox.shrink();
    }

    return BlocBuilder<FileMetadataBloc, FileMetadataState>(
      builder: (context, state) {
        if (state is FileMetadataLoading) {
          return _buildLoadingWidget();
        } else if (state is FileMetadataLoaded) {
          return _buildFileInfoWidget(state.metadata);
        } else if (state is FileMetadataError) {
          return _buildErrorWidget(state.message);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: const Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          SizedBox(width: 12),
          Text('Loading file info...'),
        ],
      ),
    );
  }

  Widget _buildFileInfoWidget(FileMetadataModel metadata) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[300]!),
      ),
      child: Row(
        children: [
          Icon(
            _getFileIcon(metadata.ext),
            color: Colors.green[700],
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  metadata.displayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  '${metadata.formattedSize} • ${metadata.ext.toUpperCase()}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          if (widget.onRemove != null) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: widget.onRemove,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.red[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  Icons.close,
                  size: 16,
                  color: Colors.red[700],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[300]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red[700],
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Error loading file: $message',
              style: TextStyle(
                color: Colors.red[700],
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getFileIcon(String extension) {
    switch (extension.toLowerCase()) {
      case '.pdf':
        return Icons.picture_as_pdf;
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
        return Icons.image;
      case '.doc':
      case '.docx':
        return Icons.description;
      case '.xls':
      case '.xlsx':
        return Icons.table_chart;
      default:
        return Icons.insert_drive_file;
    }
  }
}
