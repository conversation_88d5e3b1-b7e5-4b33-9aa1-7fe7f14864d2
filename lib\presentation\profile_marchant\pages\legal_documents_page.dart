import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';

import 'package:wesell/data/models/profile_marchant_model.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_dropdown_form_field.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';

import 'package:wesell/presentation/widgets/file_upload_widget.dart';
import '../bloc/profile_marchant_bloc.dart';
import '../bloc/profile_marchant_event.dart';
import '../bloc/profile_marchant_state.dart';

class LegalDocumentsPage extends StatefulWidget {
  final ProfileMarchantModel profileData;

  const LegalDocumentsPage({super.key, required this.profileData});

  @override
  _LegalDocumentsPageState createState() => _LegalDocumentsPageState();
}

class _LegalDocumentsPageState extends State<LegalDocumentsPage> {
  final _formKey = GlobalKey<FormState>();
  final _companyLegalNameController = TextEditingController();
  final _crNumberController = TextEditingController();
  final _investedCapitalController = TextEditingController();
  
  String? _selectedCompanySize;
  String? _selectedInvestedCapitalUnit;
  String? crDocumentId;
  String? licenseDocumentId;

  final List<String> _companySizes = ['[0-50]', '[50-100]', '[100-200]', '[300+]'];
  final List<String> _capitalUnits = [ 'SAR', 'USD'];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    final companyDetails = widget.profileData.company;
    if (companyDetails != null) {
      _companyLegalNameController.text = companyDetails.companyLegalName ?? '';
      _selectedCompanySize = companyDetails.companySize;
      _crNumberController.text = companyDetails.crNumber ?? '';
      _investedCapitalController.text = companyDetails.investedCapital?.toString() ?? '';
      _selectedInvestedCapitalUnit = companyDetails.investedCapitalUnit;
      if(companyDetails.crDocument != null && companyDetails.crDocument!.isNotEmpty) crDocumentId = companyDetails.crDocument!;
      if(companyDetails.licenseDocument != null && companyDetails.licenseDocument!.isNotEmpty) licenseDocumentId = companyDetails.licenseDocument!;
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }



  void _handleFormSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<ProfileMarchantBloc>().add(UpdateLegalDocumentsEvent(
        companyLegalName: _companyLegalNameController.text.trim(),
        companySize: _selectedCompanySize ?? '',
        crNumber: _crNumberController.text.trim(),
        investedCapital: int.tryParse(_investedCapitalController.text.trim()) ?? 0,
        investedCapitalUnit: _selectedInvestedCapitalUnit ?? '',
        crDocument: crDocumentId,
        licenseDocument: licenseDocumentId,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: BlocListener<ProfileMarchantBloc, ProfileMarchantState>(
        listener: (context, state) {
          if (state is UpdateLegalDocumentsSuccess) {
            _showSnackBar(state.message);
            // Refresh profile data after successful update
            context.read<ProfileMarchantBloc>().add(GetProfileEvent());
          } else if (state is UpdateLegalDocumentsError) {
            _showSnackBar(state.message);
          }
        },
        child: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Scaffold(
            backgroundColor: AppColors.whiteTheme,
            appBar: AppBar(
              backgroundColor: AppColors.whiteTheme,
              title: const Text(AppStrings.legalDocuments),
              leading: const BackButton(color: AppColors.blackTextTheme),
            ),
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                autovalidateMode: AutovalidateMode.onUserInteraction,
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Company Name Field
                    CustomTextField(
                      controller: _companyLegalNameController,
                      hintText: AppStrings.companyLegalName,
                      validator: (value) => Validators.validateRequired(value, AppStrings.companyLegalName),
                    ),
                    const SizedBox(height: 16),
                   CustomDropdownFormField<String>(
                      value: _selectedCompanySize,
                      items: _companySizes,
                      hintText: AppStrings.selectCompanySize,
                      itemLabel: (item) => item,
                      validator: (value) => Validators.validateRequired(value, AppStrings.pleaseSelectCompanySize,),
                      onChanged: (val) => setState(() => _selectedCompanySize = val),
                    ),
                    const SizedBox(height: 16),
                    
                    // CR Number Field
                    CustomTextField(
                      controller: _crNumberController,
                      hintText: AppStrings.crNumber,
                      validator: (value) => Validators.validateMinLength(value,10, AppStrings.crNumber),
                    ),
                    const SizedBox(height: 16),
                    
                    // Invested Capital Field
                   CustomTextField(
                      controller: _investedCapitalController,
                      hintText: AppStrings.companyInvestedCapital,
                      keyboardType: TextInputType.number,
                      validator: (value) =>
                          Validators.validateRequired(value, AppStrings.companyInvestedCapital),
                    ),
                    const SizedBox(height: 16),
                    
                    // Invested Capital Unit Dropdown
                    CustomDropdownFormField<String>(
                      value: _selectedInvestedCapitalUnit,
                      items: _capitalUnits,
                      hintText: AppStrings.selectUnit,
                      itemLabel: (item) => item,
                      validator: (value) => Validators.validateRequired(value, AppStrings.selectUnit),
                      onChanged: (val) => setState(() => _selectedInvestedCapitalUnit = val),
                    ),
                    const SizedBox(height: 24),

                    // File Upload Widgets
                    FileUploadWidget(
                      label: AppStrings.attachCrDocuments,
                      uploadId: 'cr_document',
                      onFileUploaded: (fileId) {
                        setState(() {
                          crDocumentId = fileId;
                        });
                      },
                      currentFileId: crDocumentId,
                    ),
                    const SizedBox(height: 16),

                    FileUploadWidget(
                      label: AppStrings.attachCompanyLicenseDocuments,
                      uploadId: 'license_document',
                      onFileUploaded: (fileId) {
                        setState(() {
                          licenseDocumentId = fileId;
                        });
                      },
                      currentFileId: licenseDocumentId,
                    ),
                    const SizedBox(height: 24),
                    
                    // Save Button
                    BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                      builder: (context, state) {
                        return CustomButton(
                          width: double.infinity,
                          backgroundColor: AppColors.primaryTheme,
                          text: AppStrings.saveChanges,
                          onPressed: state is UpdateLegalDocumentsLoading ? null : _handleFormSubmit,
                          isLoading: state is UpdateLegalDocumentsLoading,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _companyLegalNameController.dispose();
    _crNumberController.dispose();
    _investedCapitalController.dispose();
    super.dispose();
  }
}
