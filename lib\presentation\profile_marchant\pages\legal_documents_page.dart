import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/core/services/image_upload_service.dart';
import 'package:wesell/data/models/profile_marchant_model.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_dropdown_form_field.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import 'package:wesell/presentation/widgets/custom_dotted_border.dart';
import 'package:wesell/presentation/widgets/file_upload_widget.dart';
import '../bloc/profile_marchant_bloc.dart';
import '../bloc/profile_marchant_event.dart';
import '../bloc/profile_marchant_state.dart';

class LegalDocumentsPage extends StatefulWidget {
  final ProfileMarchantModel profileData;

  const LegalDocumentsPage({super.key, required this.profileData});

  @override
  _LegalDocumentsPageState createState() => _LegalDocumentsPageState();
}

class _LegalDocumentsPageState extends State<LegalDocumentsPage> {
  final _formKey = GlobalKey<FormState>();
  final _companyLegalNameController = TextEditingController();
  final _crNumberController = TextEditingController();
  final _investedCapitalController = TextEditingController();
  
  String? _selectedCompanySize;
  String? _selectedInvestedCapitalUnit;
  String crDocumentId = '';
  String licenseDocumentId = '';

  final List<String> _companySizes = ['1-10', '11-50', '50-100', '100+'];
  final List<String> _capitalUnits = [ 'SAR', 'USD'];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    final companyDetails = widget.profileData.company;
    if (companyDetails != null) {
      _companyLegalNameController.text = companyDetails.companyLegalName ?? '';
      _selectedCompanySize = companyDetails.companySize;
      _crNumberController.text = companyDetails.crNumber ?? '';
      _investedCapitalController.text = companyDetails.investedCapital?.toString() ?? '';
      _selectedInvestedCapitalUnit = companyDetails.investedCapitalUnit;
      crDocumentId = companyDetails.crDocument ?? '';
      licenseDocumentId = companyDetails.licenseDocument ?? '';
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  Future<void> _pickAndUploadCRDocument() async {
    try {
      final file = await ImageUploadService.pickImageFromGallery();
      if (file != null && mounted) {
        final fileName = ImageUploadService.generateImageFileName(file.path.split('/').last);
        context.read<ProfileMarchantBloc>().add(UploadImageEvent(
          file: file,
          fileName: fileName,
        ));
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(e.toString());
      }
    }
  }

  Future<void> _pickAndUploadLicenseDocument() async {
    try {
      final file = await ImageUploadService.pickImageFromGallery();
      if (file != null && mounted) {
        final fileName = ImageUploadService.generateImageFileName(file.path.split('/').last);
        context.read<ProfileMarchantBloc>().add(UploadImageEvent(
          file: file,
          fileName: fileName,
        ));
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(e.toString());
      }
    }
  }

  void _handleFormSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<ProfileMarchantBloc>().add(UpdateLegalDocumentsEvent(
        companyLegalName: _companyLegalNameController.text.trim(),
        companySize: _selectedCompanySize ?? '',
        crNumber: _crNumberController.text.trim(),
        investedCapital: int.tryParse(_investedCapitalController.text.trim()) ?? 0,
        investedCapitalUnit: _selectedInvestedCapitalUnit ?? '',
        crDocument: crDocumentId,
        licenseDocument: licenseDocumentId,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: BlocListener<ProfileMarchantBloc, ProfileMarchantState>(
        listener: (context, state) {
          if (state is UpdateLegalDocumentsSuccess) {
            _showSnackBar(state.message);
            // Refresh profile data after successful update
            context.read<ProfileMarchantBloc>().add(GetProfileEvent());
          } else if (state is UpdateLegalDocumentsError) {
            _showSnackBar(state.message);
          } else if (state is ImageUploadSuccess) {
            setState(() {
              // For simplicity, we'll use the same upload for both documents
              // In a real app, you might want to track which upload is for which document
              if (crDocumentId.isEmpty) {
                crDocumentId = state.fileId;
              } else {
                licenseDocumentId = state.fileId;
              }
            });
            _showSnackBar('Document uploaded successfully');
          } else if (state is ImageUploadError) {
            _showSnackBar(state.message);
          }
        },
        child: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Scaffold(
            backgroundColor: AppColors.whiteTheme,
            appBar: AppBar(
              backgroundColor: AppColors.whiteTheme,
              title: const Text(AppStrings.legalDocuments),
              leading: const BackButton(color: AppColors.blackTextTheme),
            ),
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                autovalidateMode: AutovalidateMode.onUserInteraction,
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Company Name Field
                    CustomTextField(
                      controller: _companyLegalNameController,
                      hintText: AppStrings.companyLegalName,
                      validator: (value) => Validators.validateRequired(value, AppStrings.companyLegalName),
                    ),
                    const SizedBox(height: 16),
                   CustomDropdownFormField<String>(
                      value: _selectedCompanySize,
                      items: _companySizes,
                      hintText: AppStrings.selectCompanySize,
                      itemLabel: (item) => item,
                      validator: (value) => Validators.validateRequired(value, AppStrings.pleaseSelectCompanySize,),
                      onChanged: (val) => setState(() => _selectedCompanySize = val),
                    ),
                    const SizedBox(height: 16),
                    
                    // CR Number Field
                    CustomTextField(
                      controller: _crNumberController,
                      hintText: AppStrings.crNumber,
                      validator: (value) => Validators.validateMinLength(value,10, AppStrings.crNumber),
                    ),
                    const SizedBox(height: 16),
                    
                    // Invested Capital Field
                   CustomTextField(
                      controller: _investedCapitalController,
                      hintText: AppStrings.companyInvestedCapital,
                      keyboardType: TextInputType.number,
                      validator: (value) =>
                          Validators.validateRequired(value, AppStrings.companyInvestedCapital),
                    ),
                    const SizedBox(height: 16),
                    
                    // Invested Capital Unit Dropdown
                    CustomDropdownFormField<String>(
                      value: _selectedInvestedCapitalUnit,
                      items: _capitalUnits,
                      hintText: AppStrings.selectUnit,
                      itemLabel: (item) => item,
                      validator: (value) => Validators.validateRequired(value, AppStrings.selectUnit),
                      onChanged: (val) => setState(() => _selectedInvestedCapitalUnit = val),
                    ),
                    const SizedBox(height: 24),
                    
                    // CR Document Upload
                    // const Text(
                    //   'CR Document',
                    //   style: TextStyle(
                    //     fontSize: 16,
                    //     fontWeight: FontWeight.w500,
                    //     color: AppColors.blackTextTheme,
                    //   ),
                    // ),
                    // const SizedBox(height: 8),
                    // GestureDetector(
                    //   onTap: _pickAndUploadCRDocument,
                    //   child: CustomDottedBorder(
                    //     child: Container(
                    //       height: 120,
                    //       width: double.infinity,
                    //       child: crDocumentId.isNotEmpty
                    //           ? const Center(
                    //               child: Column(
                    //                 mainAxisAlignment: MainAxisAlignment.center,
                    //                 children: [
                    //                   Icon(Icons.check_circle, color: AppColors.primaryTheme, size: 40),
                    //                   SizedBox(height: 8),
                    //                   Text('CR Document Uploaded'),
                    //                 ],
                    //               ),
                    //             )
                    //           : const Center(
                    //               child: Column(
                    //                 mainAxisAlignment: MainAxisAlignment.center,
                    //                 children: [
                    //                   Icon(Icons.upload_file, color: AppColors.iconLightTheme, size: 40),
                    //                   SizedBox(height: 8),
                    //                   Text('Upload CR Document'),
                    //                 ],
                    //               ),
                    //             ),
                    //     ),
                    //   ),
                    // ),
                    // const SizedBox(height: 16),
                    
                    // // License Document Upload
                    // const Text(
                    //   'License Document',
                    //   style: TextStyle(
                    //     fontSize: 16,
                    //     fontWeight: FontWeight.w500,
                    //     color: AppColors.blackTextTheme,
                    //   ),
                    // ),
                    // const SizedBox(height: 8),
                    // GestureDetector(
                    //   onTap: _pickAndUploadLicenseDocument,
                    //   child: CustomDottedBorder(
                    //     child: Container(
                    //       height: 120,
                    //       width: double.infinity,
                    //       child: licenseDocumentId.isNotEmpty
                    //           ? const Center(
                    //               child: Column(
                    //                 mainAxisAlignment: MainAxisAlignment.center,
                    //                 children: [
                    //                   Icon(Icons.check_circle, color: AppColors.primaryTheme, size: 40),
                    //                   SizedBox(height: 8),
                    //                   Text('License Document Uploaded'),
                    //                 ],
                    //               ),
                    //             )
                    //           : const Center(
                    //               child: Column(
                    //                 mainAxisAlignment: MainAxisAlignment.center,
                    //                 children: [
                    //                   Icon(Icons.upload_file, color: AppColors.iconLightTheme, size: 40),
                    //                   SizedBox(height: 8),
                    //                   Text('Upload License Document'),
                    //                 ],
                    //               ),
                    //             ),
                    //     ),
                    //   ),
                    // ),
                    // const SizedBox(height: 24),


                     FileUploadWidget(
                    label: AppStrings.attachCrDocuments,
                    uploadId: 'cr_document',
                    onFileUploaded: (fileId) {
                      setState(() {
                        crDocumentId = fileId;
                      });
                    },
                    currentFileId: crDocumentId,
                  ),

                  const SizedBox(height: 16),

                  FileUploadWidget(
                    label: AppStrings.attachCompanyLicenseDocuments,
                    uploadId: 'license_document',
                    onFileUploaded: (fileId) {
                      setState(() {
                        licenseDocumentId = fileId;
                      });
                    },
                    currentFileId: licenseDocumentId,
                  ),
                    
                    // Save Button
                    BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                      builder: (context, state) {
                        return CustomButton(
                          width: double.infinity,
                          backgroundColor: AppColors.primaryTheme,
                          text: AppStrings.saveChanges,
                          onPressed: state is UpdateLegalDocumentsLoading ? null : _handleFormSubmit,
                          isLoading: state is UpdateLegalDocumentsLoading,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _companyLegalNameController.dispose();
    _crNumberController.dispose();
    _investedCapitalController.dispose();
    super.dispose();
  }
}
