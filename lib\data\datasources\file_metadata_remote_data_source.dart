import '../../core/network/dio_client.dart';
import '../models/file_metadata_model.dart';

abstract class FileMetadataRemoteDataSource {
  Future<FileMetadataModel> getFileMetadata(String fileId);
}

class FileMetadataRemoteDataSourceImpl implements FileMetadataRemoteDataSource {
  final DioClient dioClient;

  FileMetadataRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<FileMetadataModel> getFileMetadata(String fileId) async {
    final response = await dioClient.get('/media/meta/$fileId');
    return FileMetadataModel.fromJson(response.data as Map<String, dynamic>);
  }
}
