import 'package:equatable/equatable.dart';
import '../../../../data/models/file_metadata_model.dart';

abstract class FileMetadataState extends Equatable {
  const FileMetadataState();

  @override
  List<Object?> get props => [];
}

class FileMetadataInitial extends FileMetadataState {
  const FileMetadataInitial();
}

class FileMetadataLoading extends FileMetadataState {
  final String fileId;

  const FileMetadataLoading({required this.fileId});

  @override
  List<Object?> get props => [fileId];
}

class FileMetadataLoaded extends FileMetadataState {
  final FileMetadataModel metadata;

  const FileMetadataLoaded({required this.metadata});

  @override
  List<Object?> get props => [metadata];
}

class FileMetadataError extends FileMetadataState {
  final String message;
  final String? fileId;

  const FileMetadataError({required this.message, this.fileId});

  @override
  List<Object?> get props => [message, fileId];
}
