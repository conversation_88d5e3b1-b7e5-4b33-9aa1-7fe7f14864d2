/// Utility class for validating multiple file uploads
class FileUploadValidator {
  final Map<String, bool> _fileStates = {};
  final Map<String, String> _requiredFiles = {};

  /// Register a required file upload
  void registerRequiredFile(String uploadId, String label) {
    _requiredFiles[uploadId] = label;
    _fileStates[uploadId] = false;
  }

  /// Update the state of a file upload
  void updateFileState(String uploadId, bool hasFile) {
    _fileStates[uploadId] = hasFile;
  }

  /// Check if all required files are uploaded
  bool areAllRequiredFilesUploaded() {
    for (String uploadId in _requiredFiles.keys) {
      if (_fileStates[uploadId] != true) {
        return false;
      }
    }
    return true;
  }

  /// Get list of missing required files
  List<String> getMissingRequiredFiles() {
    List<String> missing = [];
    for (String uploadId in _requiredFiles.keys) {
      if (_fileStates[uploadId] != true) {
        missing.add(_requiredFiles[uploadId]!);
      }
    }
    return missing;
  }

  /// Get validation error message for missing files
  String? getValidationError() {
    List<String> missing = getMissingRequiredFiles();
    if (missing.isEmpty) {
      return null;
    }
    
    if (missing.length == 1) {
      return '${missing.first} is required';
    } else if (missing.length == 2) {
      return '${missing.first} and ${missing.last} are required';
    } else {
      String lastFile = missing.removeLast();
      return '${missing.join(', ')}, and $lastFile are required';
    }
  }

  /// Clear all file states
  void clear() {
    _fileStates.clear();
    _requiredFiles.clear();
  }

  /// Get the current state of a specific file
  bool getFileState(String uploadId) {
    return _fileStates[uploadId] ?? false;
  }

  /// Check if a specific file is required
  bool isFileRequired(String uploadId) {
    return _requiredFiles.containsKey(uploadId);
  }
}
