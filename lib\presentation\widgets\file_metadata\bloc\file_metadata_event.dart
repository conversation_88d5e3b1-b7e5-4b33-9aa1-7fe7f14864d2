import 'package:equatable/equatable.dart';

abstract class FileMetadataEvent extends Equatable {
  const FileMetadataEvent();

  @override
  List<Object?> get props => [];
}

class LoadFileMetadata extends FileMetadataEvent {
  final String fileId;

  const LoadFileMetadata({required this.fileId});

  @override
  List<Object?> get props => [fileId];
}

class ClearFileMetadata extends FileMetadataEvent {
  const ClearFileMetadata();
}
