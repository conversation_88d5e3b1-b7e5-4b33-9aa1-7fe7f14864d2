import 'package:flutter/material.dart';
import 'validated_file_upload_widget.dart';
import '../../core/utils/file_upload_validator.dart';

/// Example usage of ValidatedFileUploadWidget in different scenarios
class FileUploadExamples extends StatefulWidget {
  const FileUploadExamples({super.key});

  @override
  State<FileUploadExamples> createState() => _FileUploadExamplesState();
}

class _FileUploadExamplesState extends State<FileUploadExamples> {
  final _formKey = GlobalKey<FormState>();
  final FileUploadValidator _validator = FileUploadValidator();
  
  // File IDs
  String? _profileImageId;
  String? _resumeId;
  String? _portfolioId;

  @override
  void initState() {
    super.initState();
    // Register required files
    _validator.registerRequiredFile('profile_image', 'Profile Image');
    _validator.registerRequiredFile('resume', 'Resume');
    // Portfolio is optional, so not registered as required
  }

  void _handleSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      if (_validator.areAllRequiredFilesUploaded()) {
        // All validations passed
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All files uploaded successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Show validation error
        final error = _validator.getValidationError();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(error ?? 'Please upload all required files'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('File Upload Examples'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Required Files',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Required Profile Image
              ValidatedFileUploadWidget(
                label: 'Profile Image',
                uploadId: 'profile_image',
                isRequired: true,
                currentFileId: _profileImageId,
                onFileUploaded: (fileId) {
                  setState(() {
                    _profileImageId = fileId;
                  });
                },
                onValidationChanged: (hasFile) {
                  _validator.updateFileState('profile_image', hasFile);
                },
              ),
              const SizedBox(height: 16),

              // Required Resume
              ValidatedFileUploadWidget(
                label: 'Resume/CV',
                uploadId: 'resume',
                isRequired: true,
                currentFileId: _resumeId,
                onFileUploaded: (fileId) {
                  setState(() {
                    _resumeId = fileId;
                  });
                },
                onValidationChanged: (hasFile) {
                  _validator.updateFileState('resume', hasFile);
                },
              ),
              const SizedBox(height: 24),

              const Text(
                'Optional Files',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Optional Portfolio
              ValidatedFileUploadWidget(
                label: 'Portfolio (Optional)',
                uploadId: 'portfolio',
                isRequired: false,
                currentFileId: _portfolioId,
                onFileUploaded: (fileId) {
                  setState(() {
                    _portfolioId = fileId;
                  });
                },
              ),
              const SizedBox(height: 32),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _handleSubmit,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Submit'),
                ),
              ),
              const SizedBox(height: 16),

              // Validation Status
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Validation Status:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Profile Image: ${_validator.getFileState('profile_image') ? '✅ Uploaded' : '❌ Required'}',
                    ),
                    Text(
                      'Resume: ${_validator.getFileState('resume') ? '✅ Uploaded' : '❌ Required'}',
                    ),
                    Text(
                      'Portfolio: ${_portfolioId != null ? '✅ Uploaded' : '⚪ Optional'}',
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'All Required Files: ${_validator.areAllRequiredFilesUploaded() ? '✅ Complete' : '❌ Incomplete'}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _validator.areAllRequiredFilesUploaded() 
                            ? Colors.green 
                            : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _validator.clear();
    super.dispose();
  }
}
