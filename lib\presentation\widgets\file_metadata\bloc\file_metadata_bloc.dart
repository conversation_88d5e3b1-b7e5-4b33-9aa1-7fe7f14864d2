import 'package:bloc/bloc.dart';
import '../../../../domain/usecases/get_file_metadata_usecase.dart';
import 'file_metadata_event.dart';
import 'file_metadata_state.dart';

class FileMetadataBloc extends Bloc<FileMetadataEvent, FileMetadataState> {
  final GetFileMetadataUseCase getFileMetadataUseCase;

  FileMetadataBloc({required this.getFileMetadataUseCase}) 
      : super(const FileMetadataInitial()) {
    on<LoadFileMetadata>(_onLoadFileMetadata);
    on<ClearFileMetadata>(_onClearFileMetadata);
  }

  Future<void> _onLoadFileMetadata(
    LoadFileMetadata event,
    Emitter<FileMetadataState> emit,
  ) async {
    emit(FileMetadataLoading(fileId: event.fileId));
    
    try {
      final result = await getFileMetadataUseCase(event.fileId);
      result.fold(
        (failure) => emit(FileMetadataError(
          message: failure.message,
          fileId: event.fileId,
        )),
        (metadata) => emit(FileMetadataLoaded(metadata: metadata)),
      );
    } catch (e) {
      emit(FileMetadataError(
        message: e.toString(),
        fileId: event.fileId,
      ));
    }
  }

  void _onClearFileMetadata(
    ClearFileMetadata event,
    Emitter<FileMetadataState> emit,
  ) {
    emit(const FileMetadataInitial());
  }
}
