import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../core/error/exceptions.dart';
import '../../domain/repositories/file_metadata_repository.dart';
import '../datasources/file_metadata_remote_data_source.dart';
import '../models/file_metadata_model.dart';

class FileMetadataRepositoryImpl implements FileMetadataRepository {
  final FileMetadataRemoteDataSource remoteDataSource;

  FileMetadataRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, FileMetadataModel>> getFileMetadata(String fileId) async {
    try {
      final result = await remoteDataSource.getFileMetadata(fileId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
