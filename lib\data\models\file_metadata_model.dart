class FileMetadataModel {
  final String id;
  final String mediaId;
  final String name;
  final String ext;
  final String type;
  final int sizeInBytes;
  final String status;
  final String createdAt;
  final String updatedAt;

  FileMetadataModel({
    required this.id,
    required this.mediaId,
    required this.name,
    required this.ext,
    required this.type,
    required this.sizeInBytes,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory FileMetadataModel.fromJson(Map<String, dynamic> json) {
    return FileMetadataModel(
      id: json['_id'] as String,
      mediaId: json['mediaId'] as String,
      name: json['name'] as String,
      ext: json['ext'] as String,
      type: json['type'] as String,
      sizeInBytes: json['sizeInBytes'] as int,
      status: json['status'] as String,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );
  }

  String get formattedSize {
    if (sizeInBytes < 1024) {
      return '${sizeInBytes} B';
    } else if (sizeInBytes < 1024 * 1024) {
      return '${(sizeInBytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(sizeInBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  String get displayName {
    return name.length > 20 ? '${name.substring(0, 17)}...' : name;
  }
}
