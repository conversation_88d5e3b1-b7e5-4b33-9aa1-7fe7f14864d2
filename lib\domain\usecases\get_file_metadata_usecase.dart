import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/file_metadata_model.dart';
import '../repositories/file_metadata_repository.dart';

class GetFileMetadataUseCase {
  final FileMetadataRepository repository;

  GetFileMetadataUseCase(this.repository);

  Future<Either<Failure, FileMetadataModel>> call(String fileId) async {
    return await repository.getFileMetadata(fileId);
  }
}
