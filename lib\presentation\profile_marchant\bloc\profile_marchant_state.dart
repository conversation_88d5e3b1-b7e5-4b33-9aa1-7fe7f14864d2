import 'package:equatable/equatable.dart';

import '../../../data/models/profile_marchant_model.dart';

abstract class ProfileMarchantState extends Equatable{}

class ProfileMarchantInitial extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class ProfileMarchantLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class ProfileMarchantLoaded extends ProfileMarchantState {
  final ProfileMarchantModel profile;

  ProfileMarchantLoaded({required this.profile});
  
  @override
  List<Object?> get props => [profile];
}

class ProfileMarchantError extends ProfileMarchantState {
  final String message;

  ProfileMarchantError({required this.message});
  @override
  List<Object?> get props => [message];
}

class ImageUploadLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class ImageUploadSuccess extends ProfileMarchantState {
  final String fileId;
  final String fileName;

  ImageUploadSuccess({
    required this.fileId,
    required this.fileName,
  });
  @override
  List<Object?> get props => [fileId, fileName];
}

class ImageUploadError extends ProfileMarchantState {
  final String message;

  ImageUploadError({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateCompanyDetailsLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateCompanyDetailsSuccess extends ProfileMarchantState {
  final String message;

  UpdateCompanyDetailsSuccess({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateCompanyDetailsError extends ProfileMarchantState {
  final String message;

  UpdateCompanyDetailsError({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateIndustryDetailsLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateIndustryDetailsSuccess extends ProfileMarchantState {
  final String message;

  UpdateIndustryDetailsSuccess({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateIndustryDetailsError extends ProfileMarchantState {
  final String message;

  UpdateIndustryDetailsError({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateCompanyLocationsLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateCompanyLocationsSuccess extends ProfileMarchantState {
  final String message;

  UpdateCompanyLocationsSuccess({required this.message});
    @override
  List<Object?> get props => [message];
}

class UpdateCompanyLocationsError extends ProfileMarchantState {
  final String message;

  UpdateCompanyLocationsError({required this.message});
  @override
  List<Object?> get props => [message];
}
