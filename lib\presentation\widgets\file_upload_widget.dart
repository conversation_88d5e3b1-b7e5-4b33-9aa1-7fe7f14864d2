import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_constants.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/utils/file_utils.dart';
import '../profile_marchant/bloc/profile_marchant_bloc.dart';
import '../profile_marchant/bloc/profile_marchant_event.dart';
import '../profile_marchant/bloc/profile_marchant_state.dart';

class FileUploadWidget extends StatefulWidget {
  final String label;
  final Function(String fileId) onFileUploaded;
  final String? currentFileId;
  final String? currentFileName;
  final String uploadId; // Unique identifier for this widget

  const FileUploadWidget({
    super.key,
    required this.label,
    required this.onFileUploaded,
    required this.uploadId,
    this.currentFileId,
    this.currentFileName,
  });

  @override
  State<FileUploadWidget> createState() => _FileUploadWidgetState();
}

class _FileUploadWidgetState extends State<FileUploadWidget> {
  String? _uploadedFileId;
  String? _uploadedFileName;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _uploadedFileId = widget.currentFileId;
    _uploadedFileName = widget.currentFileName;
    _loadFileMetadata();
  }

  @override
  void didUpdateWidget(FileUploadWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentFileId != widget.currentFileId) {
      setState(() {
        _uploadedFileId = widget.currentFileId;
        _uploadedFileName = widget.currentFileName;
      });
      _loadFileMetadata();
    }
  }

  void _loadFileMetadata() {
    if (_uploadedFileId != null && _uploadedFileId!.isNotEmpty) {
      context.read<ProfileMarchantBloc>().add(GetFileMetadataEvent(fileId: _uploadedFileId!));
    }
  }

  Future<void> _pickAndUploadFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final fileName = result.files.single.name;

        // Validate file
        final validationError = FileUtils.validateFile(file, fileName);
        if (validationError != null) {
          _showErrorSnackBar(validationError);
          return;
        }
        if (mounted) {
          try {
            context.read<ProfileMarchantBloc>().add(
              UploadImageEvent(
                file: file,
                fileName: fileName,
                uploadId: widget.uploadId,
              ),
            );
          } catch (e) {
            Exception(e.toString());
          }
        }
      }
    } catch (e) {
      _showErrorSnackBar('Failed to pick file: ${e.toString()}');
    }
  }

  void _removeFile() {
    setState(() {
      _uploadedFileId = null;
      _uploadedFileName = null;
      _isUploading = false;
    });
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget child = Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        border: Border.all(
          color: _uploadedFileId != null ? Colors.green : Colors.grey,
          style: BorderStyle.solid,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: _uploadedFileId != null
          ? _buildUploadedFileWidget(_uploadedFileId!)
          : _buildUploadWidget(),
    );

    try {
      return BlocListener<ProfileMarchantBloc, ProfileMarchantState>(
         listener: (context, state) {
            if (state is ImageUploadLoading && state.uploadId == widget.uploadId) {
              setState(() {
                _isUploading = true;
              });
            } else if (state is ImageUploadSuccess && state.uploadId == widget.uploadId) {
              setState(() {
                _uploadedFileId = state.fileId;
                _uploadedFileName = state.fileName;
                _isUploading = false;
              });
              widget.onFileUploaded(state.fileId);
              _showSuccessSnackBar(AppStrings.fileUploadSuccess);
            } else if (state is ImageUploadError && state.uploadId == widget.uploadId) {
              setState(() {
                _isUploading = false;
              });
              _showErrorSnackBar(state.message);
            }
          },
        child: child,
      );
    } catch (e) {
      Exception(e.toString());
    }
    return child;
  }

  Widget _buildUploadWidget() {
    return InkWell(
      onTap: _isUploading ? null : _pickAndUploadFile,
      child: Column(
        children: [
          Text(
            widget.label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          if (_isUploading)
            const CircularProgressIndicator()
          else
            const Icon(
              Icons.upload_file,
              size: 40,
              color: AppColors.textgreyTheme,
            ),
          if (_isUploading)
            const Padding(
              padding: EdgeInsets.only(top: 8),
              child: Text('Uploading...'),
            ),
        ],
      ),
    );
  }

  Widget _buildUploadedFileWidget(currentFileId) {
    return Column(
      children: [
        Text(
          widget.label,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 20,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                _uploadedFileName ?? 'File uploaded',
                style: const TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        // const SizedBox(height: 8),
         Padding(
           padding: const EdgeInsets.only(right:10.0,left: 10.0),
           child: Row(
             mainAxisAlignment: MainAxisAlignment.end,
             children: [
              BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                builder: (context, state) {
                  // String fileName = 'File uploaded';
                  // String extention = '';

                  // Only update if the state is for THIS specific file
                  if (state is FileMetadataLoaded && state.fileId == _uploadedFileId) {
                    // fileName = state.name;
                    // extention = state.ext;
                    print("----------------------------"+state.name);
                    return Flexible(
                    child: Row(
                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            //displayExtention(state.ext, _uploadedFileId),
                            const SizedBox(width: 10),
                            Text(state.name),
                          ],
                        ),
                         InkWell(
                        onTap: _removeFile,
                        child: Icon(Icons.download, color: AppColors.iconLightTheme, size: 25)
                      ),
                      ],
                    ),
                  );
                  } else if (state is FileMetadataError && state.fileId == _uploadedFileId) {
                    // fileName = 'Error loading file';
                    return Text(state.message);
                  } else if (state is FileMetadataLoading && state.fileId == _uploadedFileId) {
                    return CircularProgressIndicator();
                    // fileName = 'Loading...';
                  }

                  // return Flexible(
                  //   child: Row(
                  //        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //     children: [
                  //       Row(
                  //         children: [
                  //           displayExtention(extention, _uploadedFileId),
                  //           const SizedBox(width: 10),
                  //           Text(fileName),
                  //         ],
                  //       ),
                  //        InkWell(
                  //       onTap: _removeFile,
                  //       child: Icon(Icons.download, color: AppColors.iconLightTheme, size: 25)
                  //     ),
                  //     ],
                  //   ),
                  // );
                },
              ),
             
              const SizedBox(width: 20),
               Container(
                 alignment: Alignment.bottomRight,
                 child: InkWell(
                  onTap: _removeFile,
                  child: Icon(Icons.delete, color: AppColors.iconLightTheme, size: 25)),
               ),
                const SizedBox(width: 5),
             ],
           ),
         ),
      ],
    );
  }

  Widget displayExtention(extention,mediaId) {
    Widget extenIcon(url){
      return SizedBox(
          height: 50,
          width: 50,
          child: Image.network(
            url,
            errorBuilder: (context, error, stackTrace) {
              return const Icon(Icons.error);
            }
          ),
        );
    }
    if(extention.contains('pdf')){
        String url = AppConstants.pdfPlaceholder;
      return extenIcon(url);
    }else if(extention.contains('jpg') || extention.contains('jpeg') || extention.contains('png')){
        String url = AppConstants.mediaBaseUrl+mediaId;
      return extenIcon(url);
    }
    return const Icon(Icons.file_copy, color: AppColors.iconLightTheme, size: 25);
  }
}
